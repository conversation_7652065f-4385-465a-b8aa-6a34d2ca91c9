import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
from datetime import datetime

class ExcelProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel数据处理工具")
        self.root.geometry("900x800")
        self.root.configure(bg='#f8f9fa')
        self.root.resizable(True, True)

        # 设置窗口图标和样式
        self.setup_styles()

        # 设置窗口居中
        self.center_window()

        # 存储选择的文件路径
        self.selected_file = None

        # 创建界面
        self.create_widgets()

    def setup_styles(self):
        """设置样式主题"""
        style = ttk.Style()
        style.theme_use('clam')

        # 配置按钮样式
        style.configure('Modern.TButton',
                       background='#007bff',
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.map('Modern.TButton',
                 background=[('active', '#0056b3'),
                           ('pressed', '#004085')])

        # 配置成功按钮样式
        style.configure('Success.TButton',
                       background='#28a745',
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.map('Success.TButton',
                 background=[('active', '#218838'),
                           ('pressed', '#1e7e34')])

        # 配置危险按钮样式
        style.configure('Danger.TButton',
                       background='#dc3545',
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.map('Danger.TButton',
                 background=[('active', '#c82333'),
                           ('pressed', '#bd2130')])

        # 配置输入框样式
        style.configure('Modern.TEntry',
                       fieldbackground='white',
                       borderwidth=1,
                       relief='solid',
                       padding=8)

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def create_section_frame(self, parent, title):
        """创建带标题的区域框架"""
        # 外层容器
        section_container = tk.Frame(parent, bg='#f8f9fa')
        section_container.pack(fill='x', pady=(0, 20))

        # 标题
        title_label = tk.Label(
            section_container,
            text=title,
            font=('微软雅黑', 14, 'bold'),
            fg='#495057',
            bg='#f8f9fa'
        )
        title_label.pack(anchor='w', pady=(0, 8))

        # 内容区域（白色背景，圆角效果）
        content_frame = tk.Frame(
            section_container,
            bg='white',
            relief='solid',
            bd=1
        )
        content_frame.pack(fill='x')

        return content_frame

    def create_widgets(self):
        """创建界面组件"""
        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)

        # 主标题区域
        title_frame = tk.Frame(main_container, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=(0, 30))

        # 主标题
        title_label = tk.Label(
            title_frame,
            text="📊 Excel数据筛选工具",
            font=('微软雅黑', 28, 'bold'),
            fg='#212529',
            bg='#f8f9fa'
        )
        title_label.pack()

        # 副标题
        subtitle_label = tk.Label(
            title_frame,
            text="智能筛选 • 快速导出 • 简单易用",
            font=('微软雅黑', 12),
            fg='#6c757d',
            bg='#f8f9fa'
        )
        subtitle_label.pack(pady=(5, 0))

        # 文件选择区域
        file_frame = self.create_section_frame(main_container, "📁 文件选择")

        # 文件选择内容
        file_content = tk.Frame(file_frame, bg='white')
        file_content.pack(fill='x', padx=20, pady=15)

        # 文件选择按钮
        self.select_btn = ttk.Button(
            file_content,
            text="🔍 选择Excel文件",
            command=self.select_file,
            style='Modern.TButton'
        )
        self.select_btn.pack(side='left')

        # 文件状态显示
        self.file_label = tk.Label(
            file_content,
            text="未选择文件",
            font=('微软雅黑', 11),
            fg='#6c757d',
            bg='white'
        )
        self.file_label.pack(side='left', padx=(20, 0))

        # 参数设置区域
        param_frame = self.create_section_frame(main_container, "⚙️ 筛选参数")

        # 参数设置内容
        param_content = tk.Frame(param_frame, bg='white')
        param_content.pack(fill='x', padx=20, pady=15)

        # 创建参数输入网格
        param_grid = tk.Frame(param_content, bg='white')
        param_grid.pack(fill='x')

        # 配置网格权重
        param_grid.columnconfigure(1, weight=1)
        param_grid.columnconfigure(3, weight=1)

        # 起始号码
        start_label = tk.Label(
            param_grid,
            text="🔢 起始号码:",
            font=('微软雅黑', 11, 'bold'),
            fg='#495057',
            bg='white'
        )
        start_label.grid(row=0, column=0, sticky='w', padx=(0, 15), pady=10)

        self.start_entry = ttk.Entry(
            param_grid,
            font=('微软雅黑', 11),
            width=25,
            style='Modern.TEntry'
        )
        self.start_entry.grid(row=0, column=1, sticky='ew', padx=(0, 30), pady=10)

        # 终止号码
        end_label = tk.Label(
            param_grid,
            text="🎯 终止号码:",
            font=('微软雅黑', 11, 'bold'),
            fg='#495057',
            bg='white'
        )
        end_label.grid(row=0, column=2, sticky='w', padx=(0, 15), pady=10)

        self.end_entry = ttk.Entry(
            param_grid,
            font=('微软雅黑', 11),
            width=25,
            style='Modern.TEntry'
        )
        self.end_entry.grid(row=0, column=3, sticky='ew', pady=10)

        # 数量显示（自动计算）
        count_label = tk.Label(
            param_grid,
            text="📊 数据数量:",
            font=('微软雅黑', 11, 'bold'),
            fg='#495057',
            bg='white'
        )
        count_label.grid(row=1, column=0, sticky='w', padx=(0, 15), pady=10)

        self.count_display = tk.Label(
            param_grid,
            text="请先输入起始和终止号码",
            font=('微软雅黑', 11),
            fg='#6c757d',
            bg='white',
            relief='solid',
            bd=1,
            padx=10,
            pady=8
        )
        self.count_display.grid(row=1, column=1, sticky='ew', padx=(0, 30), pady=10)

        # 添加示例提示
        example_label = tk.Label(
            param_grid,
            text="💡 数量 = 终止号码 - 起始号码 + 1",
            font=('微软雅黑', 9),
            fg='#6c757d',
            bg='white'
        )
        example_label.grid(row=1, column=2, columnspan=2, sticky='w', pady=(0, 10))

        # 绑定输入框事件，实时计算数量
        self.start_entry.bind('<KeyRelease>', self.update_count_display)
        self.end_entry.bind('<KeyRelease>', self.update_count_display)

        # 操作按钮区域
        button_frame = tk.Frame(main_container, bg='#f8f9fa')
        button_frame.pack(pady=30)

        # 按钮容器
        button_container = tk.Frame(button_frame, bg='#f8f9fa')
        button_container.pack()

        # 执行筛选按钮
        self.process_btn = ttk.Button(
            button_container,
            text="🚀 执行筛选",
            command=self.process_data,
            style='Success.TButton'
        )
        self.process_btn.pack(side='left', padx=15)

        # 导出结果按钮
        self.export_btn = ttk.Button(
            button_container,
            text="📤 导出结果",
            command=self.export_data,
            style='Danger.TButton',
            state='disabled'
        )
        self.export_btn.pack(side='left', padx=15)

        # 清空按钮
        self.clear_btn = ttk.Button(
            button_container,
            text="🗑️ 清空数据",
            command=self.clear_data,
            style='Modern.TButton'
        )
        self.clear_btn.pack(side='left', padx=15)

        # 结果显示区域
        result_frame = self.create_section_frame(main_container, "📋 处理结果")

        # 结果显示内容
        result_content = tk.Frame(result_frame, bg='white', height=300)
        result_content.pack(fill='both', expand=True, padx=20, pady=15)
        result_content.pack_propagate(False)  # 防止内容改变框架大小

        # 创建文本框显示结果
        text_frame = tk.Frame(result_content, bg='white')
        text_frame.pack(fill='both', expand=True)

        self.result_text = tk.Text(
            text_frame,
            font=('Consolas', 11),
            bg='#f8f9fa',
            fg='#495057',
            relief='solid',
            bd=1,
            wrap='word',
            padx=10,
            pady=10
        )

        # 添加滚动条
        scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)

        self.result_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # 初始化欢迎信息
        welcome_msg = """
🎉 欢迎使用Excel数据筛选工具！

📝 使用步骤：
1. 点击"选择Excel文件"选择要处理的文件
2. 输入筛选参数（起始号码、终止号码）
3. 系统会自动计算数据数量
4. 点击"执行筛选"开始处理数据
5. 查看处理结果，然后点击"导出结果"保存

💡 提示：程序会自动从Excel第二行开始读取A列数据进行筛选
        """
        self.result_text.insert('1.0', welcome_msg)
        self.result_text.config(state='disabled')

        # 存储筛选后的数据
        self.filtered_data = None

    def select_file(self):
        """选择Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )

        if file_path:
            self.selected_file = file_path
            filename = os.path.basename(file_path)
            self.file_label.config(text=f"已选择: {filename}", fg='#27ae60')
            self.log_message(f"已选择文件: {filename}")

    def log_message(self, message):
        """在结果区域显示消息"""
        self.result_text.config(state='normal')
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.result_text.insert('end', f"[{timestamp}] {message}\n")
        self.result_text.see('end')
        self.result_text.config(state='disabled')
        self.root.update()

    def clear_data(self):
        """清空所有数据和输入"""
        # 清空输入框
        self.start_entry.delete(0, 'end')
        self.end_entry.delete(0, 'end')

        # 重置数量显示
        self.count_display.config(
            text="请先输入起始和终止号码",
            fg='#6c757d'
        )

        # 重置文件选择
        self.selected_file = None
        self.file_label.config(text="未选择文件", fg='#6c757d')

        # 清空结果数据
        self.filtered_data = None

        # 禁用导出按钮
        self.export_btn.config(state='disabled')

        # 清空并重置结果显示
        self.result_text.config(state='normal')
        self.result_text.delete('1.0', 'end')
        welcome_msg = """
🎉 欢迎使用Excel数据筛选工具！

📝 使用步骤：
1. 点击"选择Excel文件"选择要处理的文件
2. 输入筛选参数（起始号码、终止号码）
3. 系统会自动计算数据数量
4. 点击"执行筛选"开始处理数据
5. 查看处理结果，然后点击"导出结果"保存

💡 提示：程序会自动从Excel第二行开始读取A列数据进行筛选
        """
        self.result_text.insert('1.0', welcome_msg)
        self.result_text.config(state='disabled')

        self.log_message("✨ 已清空所有数据，可以重新开始操作")

    def update_count_display(self, event=None):
        """实时更新数量显示"""
        try:
            start_text = self.start_entry.get().strip()
            end_text = self.end_entry.get().strip()

            if start_text and end_text:
                start_num = int(start_text)
                end_num = int(end_text)

                if start_num < end_num:
                    count = end_num - start_num + 1
                    self.count_display.config(
                        text=f"{count:,} 条数据",
                        fg='#28a745'
                    )
                elif start_num == end_num:
                    self.count_display.config(
                        text="1 条数据",
                        fg='#28a745'
                    )
                else:
                    self.count_display.config(
                        text="起始号码不能大于终止号码",
                        fg='#dc3545'
                    )
            else:
                self.count_display.config(
                    text="请先输入起始和终止号码",
                    fg='#6c757d'
                )
        except ValueError:
            self.count_display.config(
                text="请输入有效的数字",
                fg='#dc3545'
            )

    def validate_inputs(self):
        """验证输入参数"""
        if not self.selected_file:
            messagebox.showerror("错误", "请先选择Excel文件！")
            return False

        if not os.path.exists(self.selected_file):
            messagebox.showerror("错误", "选择的文件不存在！")
            return False

        try:
            start_num = int(self.start_entry.get().strip())
            end_num = int(self.end_entry.get().strip())

            if start_num >= end_num:
                messagebox.showerror("错误", "起始号码必须小于终止号码！")
                return False

            return True

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字！")
            return False

    def process_data(self):
        """处理数据"""
        if not self.validate_inputs():
            return

        try:
            self.log_message("开始读取Excel文件...")

            # 读取Excel文件
            df = pd.read_excel(self.selected_file)
            self.log_message(f"成功读取文件，共 {len(df)} 行数据")

            # 获取输入参数
            start_num = int(self.start_entry.get().strip())
            end_num = int(self.end_entry.get().strip())
            count = end_num - start_num + 1  # 自动计算数量

            self.log_message(f"筛选条件: 起始号码={start_num}, 终止号码={end_num}, 数据数量={count}")

            # 检查是否有数据
            if len(df) == 0:
                messagebox.showerror("错误", "Excel文件中没有数据！")
                return

            # 获取第一列数据（从第二行开始，即索引1开始）
            if len(df) < 2:
                messagebox.showerror("错误", "Excel文件数据不足，至少需要2行数据！")
                return

            # 从第二行开始读取第一列数据
            first_column = df.iloc[1:, 0]  # 从索引1开始，取第一列

            self.log_message(f"从第二行开始读取，共 {len(first_column)} 行数据")

            # 筛选在范围内的数据
            mask = (first_column >= start_num) & (first_column <= end_num)
            filtered_indices = first_column[mask].index

            if len(filtered_indices) == 0:
                messagebox.showwarning("警告", f"在范围 {start_num} - {end_num} 内没有找到数据！")
                self.log_message("筛选结果: 没有找到符合条件的数据")
                return

            self.log_message(f"找到 {len(filtered_indices)} 条符合范围条件的数据")

            # 限制数量
            if len(filtered_indices) > count:
                filtered_indices = filtered_indices[:count]
                self.log_message(f"按需要数量限制，取前 {count} 条数据")

            # 获取筛选后的完整行数据
            self.filtered_data = df.iloc[filtered_indices]

            self.log_message(f"筛选完成！共筛选出 {len(self.filtered_data)} 条数据")
            self.log_message("数据预览:")

            # 显示前几行数据预览
            preview_count = min(5, len(self.filtered_data))
            for i in range(preview_count):
                row_data = self.filtered_data.iloc[i]
                self.log_message(f"  行{filtered_indices[i]+1}: {row_data.iloc[0]} | {row_data.iloc[1] if len(row_data) > 1 else 'N/A'}")

            if len(self.filtered_data) > preview_count:
                self.log_message(f"  ... 还有 {len(self.filtered_data) - preview_count} 条数据")

            # 启用导出按钮
            self.export_btn.config(state='normal')
            self.log_message("可以点击'导出结果'按钮保存数据")

        except Exception as e:
            messagebox.showerror("错误", f"处理数据时发生错误: {str(e)}")
            self.log_message(f"错误: {str(e)}")

    def export_data(self):
        """导出筛选后的数据"""
        if self.filtered_data is None or len(self.filtered_data) == 0:
            messagebox.showerror("错误", "没有可导出的数据！请先执行筛选。")
            return

        try:
            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存筛选结果",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if save_path:
                self.log_message("正在导出数据...")

                # 保存到新的Excel文件
                self.filtered_data.to_excel(save_path, index=False)

                filename = os.path.basename(save_path)
                self.log_message(f"数据已成功导出到: {filename}")
                self.log_message(f"导出 {len(self.filtered_data)} 条数据")

                messagebox.showinfo("成功", f"数据已成功导出到:\n{filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出数据时发生错误: {str(e)}")
            self.log_message(f"导出错误: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = ExcelProcessorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()